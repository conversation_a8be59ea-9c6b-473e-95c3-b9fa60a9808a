#!/usr/bin/env python3
"""
Script to combine PNG screenshots into a single PDF file.
"""

import os
import glob
from PIL import Image
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.utils import ImageReader
import sys

def get_image_files(directory="."):
    """Get all PNG files in the directory, sorted by filename."""
    pattern = os.path.join(directory, "IMG_*.PNG")
    files = glob.glob(pattern)
    files.sort()  # Sort to maintain order
    return files

def calculate_image_size(img_width, img_height, max_width, max_height):
    """Calculate the size to fit image within the given dimensions while maintaining aspect ratio."""
    # Calculate scaling factors
    width_scale = max_width / img_width
    height_scale = max_height / img_height
    
    # Use the smaller scale to ensure the image fits within bounds
    scale = min(width_scale, height_scale)
    
    new_width = int(img_width * scale)
    new_height = int(img_height * scale)
    
    return new_width, new_height

def create_pdf_from_images(image_files, output_filename="screenshots.pdf"):
    """Create a PDF from a list of image files."""
    if not image_files:
        print("No image files found!")
        return False
    
    print(f"Creating PDF with {len(image_files)} images...")
    
    # Create PDF canvas
    c = canvas.Canvas(output_filename, pagesize=A4)
    page_width, page_height = A4
    
    # Add some margin
    margin = 50
    max_img_width = page_width - (2 * margin)
    max_img_height = page_height - (2 * margin)
    
    for i, img_file in enumerate(image_files):
        print(f"Processing image {i+1}/{len(image_files)}: {os.path.basename(img_file)}")
        
        try:
            # Open and process the image
            with Image.open(img_file) as img:
                # Convert to RGB if necessary (for PNG with transparency)
                if img.mode in ('RGBA', 'LA', 'P'):
                    # Create a white background
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    if img.mode == 'P':
                        img = img.convert('RGBA')
                    background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                    img = background
                elif img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # Calculate the size to fit on the page
                img_width, img_height = img.size
                new_width, new_height = calculate_image_size(
                    img_width, img_height, max_img_width, max_img_height
                )
                
                # Center the image on the page
                x = (page_width - new_width) / 2
                y = (page_height - new_height) / 2
                
                # Create ImageReader object for reportlab
                img_reader = ImageReader(img)
                
                # Draw the image on the PDF
                c.drawImage(img_reader, x, y, width=new_width, height=new_height)
                
                # Add page number at the bottom
                c.setFont("Helvetica", 10)
                c.drawString(page_width - 100, 20, f"Page {i+1} of {len(image_files)}")
                c.drawString(50, 20, os.path.basename(img_file))
                
                # Start a new page for the next image (except for the last image)
                if i < len(image_files) - 1:
                    c.showPage()
                    
        except Exception as e:
            print(f"Error processing {img_file}: {str(e)}")
            continue
    
    # Save the PDF
    c.save()
    print(f"PDF created successfully: {output_filename}")
    return True

def main():
    """Main function to create PDF from screenshots."""
    print("Screenshot to PDF Converter")
    print("=" * 30)
    
    # Get all image files
    image_files = get_image_files()
    
    if not image_files:
        print("No PNG files found in the current directory!")
        return
    
    print(f"Found {len(image_files)} image files:")
    for img_file in image_files[:5]:  # Show first 5
        print(f"  - {os.path.basename(img_file)}")
    if len(image_files) > 5:
        print(f"  ... and {len(image_files) - 5} more")
    
    # Create the PDF
    output_filename = "combined_screenshots.pdf"
    success = create_pdf_from_images(image_files, output_filename)
    
    if success:
        print(f"\n✅ Successfully created: {output_filename}")
        print(f"📄 Total pages: {len(image_files)}")
        
        # Get file size
        file_size = os.path.getsize(output_filename)
        file_size_mb = file_size / (1024 * 1024)
        print(f"📦 File size: {file_size_mb:.2f} MB")
    else:
        print("\n❌ Failed to create PDF")

if __name__ == "__main__":
    main()
